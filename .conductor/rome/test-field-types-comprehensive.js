#!/usr/bin/env node

/**
 * Comprehensive test script for NocoBase field types
 * Tests all implemented field types including:
 * - Basic types (string, number, boolean, etc.)
 * - Identifier types (uid, uuid, nanoid)
 * - Security types (password, encryption)
 * - Time types (datetime, date, time, createdAt, updatedAt, birthday)
 */

const { spawn } = require('child_process');
const path = require('path');

// Test configuration
const config = {
  serverUrl: 'https://n.astra.xin/api',
  appId: 'mcp_playground',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
  testCollection: 'test_fields_comprehensive'
};

// Test cases for all field types
const testCases = [
  // Basic types
  {
    name: 'String Field',
    type: 'string',
    options: { uiSchema: { type: 'string', 'x-component': 'Input', title: 'String Field' } }
  },
  {
    name: 'Text Field',
    type: 'text',
    options: { uiSchema: { type: 'string', 'x-component': 'Input.TextArea', title: 'Text Field' } }
  },
  {
    name: 'Number Field',
    type: 'number',
    options: { uiSchema: { type: 'number', 'x-component': 'InputNumber', title: 'Number Field' } }
  },
  {
    name: 'Integer Field',
    type: 'integer',
    options: { uiSchema: { type: 'number', 'x-component': 'InputNumber', title: 'Integer Field' } }
  },
  {
    name: 'Percent Field',
    type: 'percent',
    options: { uiSchema: { type: 'number', 'x-component': 'Input.Percent', title: 'Percent Field' } }
  },
  {
    name: 'Boolean Field',
    type: 'boolean',
    options: { uiSchema: { type: 'boolean', 'x-component': 'Checkbox', title: 'Boolean Field' } }
  },
  
  // Identifier types
  {
    name: 'UID Field',
    type: 'uid',
    options: { 
      uiSchema: { type: 'string', 'x-component': 'Input', title: 'UID Field' },
      pattern: 'FL{{sequence}}',
      digits: 6
    }
  },
  {
    name: 'UUID Field',
    type: 'uuid',
    options: { 
      uiSchema: { type: 'string', 'x-component': 'Input', title: 'UUID Field' },
      version: 4
    }
  },
  {
    name: 'NanoID Field',
    type: 'nanoid',
    options: { 
      uiSchema: { type: 'string', 'x-component': 'Input', title: 'NanoID Field' },
      length: 21
    }
  },
  
  // Security types
  {
    name: 'Password Field',
    type: 'password',
    options: { 
      uiSchema: { type: 'string', 'x-component': 'Input.Password', title: 'Password Field' },
      checkStrength: true,
      minLength: 8
    }
  },
  {
    name: 'Encryption Field',
    type: 'encryption',
    options: { 
      uiSchema: { type: 'string', 'x-component': 'Input', title: 'Encryption Field' },
      algorithm: 'aes-256-gcm'
    }
  },
  
  // Time types
  {
    name: 'Datetime Field',
    type: 'datetime',
    options: { 
      uiSchema: { type: 'datetime', 'x-component': 'DatePicker', title: 'Datetime Field' },
      showTime: true
    }
  },
  {
    name: 'Date Field',
    type: 'date',
    options: { 
      uiSchema: { type: 'date', 'x-component': 'DatePicker', title: 'Date Field' }
    }
  },
  {
    name: 'Time Field',
    type: 'time',
    options: { 
      uiSchema: { type: 'time', 'x-component': 'TimePicker', title: 'Time Field' }
    }
  },
  {
    name: 'Created At Field',
    type: 'createdAt',
    options: { 
      uiSchema: { type: 'datetime', 'x-component': 'DatePicker', title: 'Created At Field' }
    }
  },
  {
    name: 'Updated At Field',
    type: 'updatedAt',
    options: { 
      uiSchema: { type: 'datetime', 'x-component': 'DatePicker', title: 'Updated At Field' }
    }
  },
  {
    name: 'Birthday Field',
    type: 'birthday',
    options: { 
      uiSchema: { type: 'date', 'x-component': 'DatePicker', title: 'Birthday Field' }
    }
  }
];

// Test data for each field type
const testData = {
  string: 'Test string value',
  text: 'This is a longer text value that can contain multiple lines and special characters.',
  number: 42.5,
  integer: 42,
  percent: 75.5,
  boolean: true,
  uid: 'FL000001', // This will be auto-generated
  uuid: '', // This will be auto-generated
  nanoid: '', // This will be auto-generated
  password: 'MySecurePassword123!',
  encryption: 'Sensitive data to encrypt',
  datetime: new Date().toISOString(),
  date: new Date().toISOString().split('T')[0],
  time: '14:30:00',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  birthday: '1990-01-01'
};

async function runTest() {
  console.log('🚀 Starting comprehensive field type tests...\n');
  
  // Start MCP server
  const server = spawn('npx', ['tsx', 'src/index.ts'], {
    stdio: ['pipe', 'pipe', 'pipe'],
    cwd: process.cwd()
  });
  
  let output = '';
  let testResults = [];
  
  server.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  server.stderr.on('data', (data) => {
    console.error('Server error:', data.toString());
  });
  
  // Wait for server to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test each field type
  for (const testCase of testCases) {
    console.log(`\n📋 Testing: ${testCase.name} (${testCase.type})`);
    
    try {
      // Create collection
      const createCollectionRequest = {
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'tools/call',
        params: {
          name: 'create_collection',
          arguments: {
            name: config.testCollection,
            title: 'Test Fields Comprehensive',
            autoGenId: true
          }
        }
      };
      
      server.stdin.write(JSON.stringify(createCollectionRequest) + '\n');
      
      // Create field
      const createFieldRequest = {
        jsonrpc: '2.0',
        id: Date.now() + 1,
        method: 'tools/call',
        params: {
          name: 'create_field',
          arguments: {
            collectionName: config.testCollection,
            fieldName: testCase.name.toLowerCase().replace(/\s+/g, '_'),
            fieldType: testCase.type,
            ...testCase.options
          }
        }
      };
      
      server.stdin.write(JSON.stringify(createFieldRequest) + '\n');
      
      // Wait for response
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create record with test data
      const createRecordRequest = {
        jsonrpc: '2.0',
        id: Date.now() + 2,
        method: 'tools/call',
        params: {
          name: 'create_record',
          arguments: {
            collectionName: config.testCollection,
            data: {
              [testCase.name.toLowerCase().replace(/\s+/g, '_')]: testData[testCase.type]
            }
          }
        }
      };
      
      server.stdin.write(JSON.stringify(createRecordRequest) + '\n');
      
      // Wait for response
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Get records to verify
      const getRecordsRequest = {
        jsonrpc: '2.0',
        id: Date.now() + 3,
        method: 'tools/call',
        params: {
          name: 'list_records',
          arguments: {
            collectionName: config.testCollection
          }
        }
      };
      
      server.stdin.write(JSON.stringify(getRecordsRequest) + '\n');
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      testResults.push({
        field: testCase.name,
        type: testCase.type,
        status: '✅ Passed'
      });
      
      console.log(`✅ ${testCase.name}: Test passed`);
      
    } catch (error) {
      console.error(`❌ ${testCase.name}: Test failed -`, error.message);
      testResults.push({
        field: testCase.name,
        type: testCase.type,
        status: '❌ Failed',
        error: error.message
      });
    }
  }
  
  // Clean up
  await new Promise(resolve => setTimeout(resolve, 2000));
  server.kill();
  
  // Print summary
  console.log('\n\n📊 Test Results Summary');
  console.log('='.repeat(50));
  
  const passed = testResults.filter(r => r.status === '✅ Passed').length;
  const failed = testResults.filter(r => r.status === '❌ Failed').length;
  
  console.log(`Total tests: ${testResults.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success rate: ${((passed / testResults.length) * 100).toFixed(2)}%`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.filter(r => r.status === '❌ Failed').forEach(result => {
      console.log(`  - ${result.field} (${result.type}): ${result.error}`);
    });
  }
  
  // Save detailed results
  const fs = require('fs');
  const reportPath = path.join(process.cwd(), 'test-results-comprehensive.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 Detailed results saved to: ${reportPath}`);
  
  process.exit(failed > 0 ? 1 : 0);
}

runTest().catch(console.error);