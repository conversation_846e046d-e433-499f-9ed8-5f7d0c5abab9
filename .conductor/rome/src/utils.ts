/**
 * Utility functions for NocoBase MCP Server
 */

// NocoBase's official UID generation algorithm
let IDX = 36,
  HEX = '';
while (IDX--) HEX += IDX.toString(36);

/**
 * Generate a unique identifier
 * Exact implementation from NocoBase's uid() function
 */
export function uid(len?: number): string {
  let str = '',
    num = len || 11;
  while (num--) str += HEX[(Math.random() * 36) | 0];
  return str;
}

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return true;
  }
  
  if (typeof value === 'string') {
    return value.trim().length === 0;
  }
  
  if (Array.isArray(value)) {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * Safely parse JSON string
 */
export function safeJsonParse(jsonString: string, defaultValue: any = null): any {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
}

/**
 * Format error message for consistent error handling
 */
export function formatError(error: any): string {
  if (error instanceof Error) {
    return error.message;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  if (error && error.message) {
    return error.message;
  }
  
  return 'Unknown error occurred';
}

/**
 * Validate required fields in an object
 */
export function validateRequiredFields(obj: any, requiredFields: string[]): string[] {
  const missingFields: string[] = [];
  
  for (const field of requiredFields) {
    if (isEmpty(obj[field])) {
      missingFields.push(field);
    }
  }
  
  return missingFields;
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse(message: string, data?: any) {
  return {
    content: [
      {
        type: 'text',
        text: data ? `${message}\n${JSON.stringify(data, null, 2)}` : message
      }
    ]
  };
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(error: any) {
  return {
    content: [
      {
        type: 'text',
        text: `Error: ${formatError(error)}`
      }
    ],
    isError: true
  };
}
