import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";

export async function registerRecordTools(server: <PERSON>cpServer, client: NocoBaseClient) {
  // List records
  server.registerTool(
    "list_records",
    {
      title: "List Records",
      description: "List records from a collection with optional filtering and pagination",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        page: z.number().optional().default(1).describe("Page number (starts from 1)"),
        pageSize: z.number().optional().default(20).describe("Number of records per page"),
        filter: z.any().optional().describe("Filter conditions (JSON object)"),
        sort: z.array(z.string()).optional().describe("Sort fields (e.g., ['name', '-createdAt'])"),
        appends: z.array(z.string()).optional().describe("Related fields to include")
      }
    },
    async ({ collection, page, pageSize, filter, sort, appends }) => {
      try {
        const options: any = { page, pageSize, filter };
        if (sort) options.sort = sort;
        if (appends) options.appends = appends;

        const result = await client.listRecords(collection, options);

        const records = result.data;
        const meta = result.meta;

        let response = `Found ${meta?.count || records.length} records in '${collection}'`;
        
        if (meta) {
          response += ` (Page ${meta.page}/${meta.totalPage}, ${meta.pageSize} per page)`;
        }

        if (records.length > 0) {
          response += '\n\nRecords:\n';
          records.forEach((record, index) => {
            response += `\n${index + 1}. Record ID: ${record.id}\n`;
            Object.entries(record).forEach(([key, value]) => {
              if (key !== 'id' && value !== null && value !== undefined) {
                const displayValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
                response += `   ${key}: ${displayValue.length > 100 ? displayValue.substring(0, 100) + '...' : displayValue}\n`;
              }
            });
          });
        } else {
          response += '\n\nNo records found.';
        }

        return {
          content: [{
            type: "text",
            text: response
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing records from '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get record
  server.registerTool(
    "get_record",
    {
      title: "Get Record",
      description: "Get a specific record by ID",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        id: z.union([z.string(), z.number()]).describe("ID of the record"),
        appends: z.array(z.string()).optional().describe("Related fields to include")
      }
    },
    async ({ collection, id, appends }) => {
      try {
        const record = await client.getRecord(collection, id, appends);

        let response = `Record from '${collection}' (ID: ${record.id}):\n\n`;
        
        Object.entries(record).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            const displayValue = typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value);
            response += `${key}: ${displayValue}\n`;
          }
        });

        return {
          content: [{
            type: "text",
            text: response
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting record ${id} from '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create record
  server.registerTool(
    "create_record",
    {
      title: "Create Record",
      description: "Create a new record in a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        data: z.any().describe("Record data (JSON object)")
      }
    },
    async ({ collection, data }) => {
      try {
        const record = await client.createRecord(collection, data);

        return {
          content: [{
            type: "text",
            text: `Successfully created record in '${collection}' with ID: ${record.id}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating record in '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update record
  server.registerTool(
    "update_record",
    {
      title: "Update Record",
      description: "Update an existing record",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        id: z.union([z.string(), z.number()]).describe("ID of the record to update"),
        data: z.any().describe("Updated data (JSON object)")
      }
    },
    async ({ collection, id, data }) => {
      try {
        const record = await client.updateRecord(collection, id, data);

        return {
          content: [{
            type: "text",
            text: `Successfully updated record ${id} in '${collection}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating record ${id} in '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete record
  server.registerTool(
    "delete_record",
    {
      title: "Delete Record",
      description: "Delete a record from a collection",
      inputSchema: {
        collection: z.string().describe("Name of the collection"),
        id: z.union([z.string(), z.number()]).describe("ID of the record to delete"),
        confirm: z.boolean().describe("Confirmation that you want to delete the record")
      }
    },
    async ({ collection, id, confirm }) => {
      if (!confirm) {
        return {
          content: [{
            type: "text",
            text: "Record deletion cancelled. Set 'confirm' to true to proceed with deletion."
          }]
        };
      }

      try {
        await client.deleteRecord(collection, id);

        return {
          content: [{
            type: "text",
            text: `Successfully deleted record ${id} from '${collection}'`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting record ${id} from '${collection}': ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
