/**
 * NocoBase 字段类型映射配置
 * 包含所有支持的字段类型及其默认配置
 */

export interface FieldTypeInfo {
  /** 字段类型名称 */
  type: string;
  /** UI 接口类型 */
  interface: string;
  /** 字段显示标题 */
  title: string;
  /** 字段描述 */
  description?: string;
  /** 默认 UI Schema 配置 */
  uiSchema?: any;
  /** 是否是系统字段 */
  isSystemField?: boolean;
  /** 字段配置选项 */
  options?: Record<string, any>;
  /** 支持的数据库类型 */
  supportedBy?: string[];
  /** 依赖的插件 */
  requiredPlugins?: string[];
  /** 实现状态 */
  implemented?: boolean;
  /** 实现优先级 */
  priority?: 'high' | 'medium' | 'low';
}

/**
 * Phase 1: 核心基础字段（已实现）
 */
export const BASIC_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  // 基础类型
  string: {
    type: 'string',
    interface: 'input',
    title: '单行文本',
    description: '单行文本输入',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  textarea: {
    type: 'text',
    interface: 'textarea',
    title: '多行文本',
    description: '多行文本输入',
    uiSchema: {
      type: 'string',
      'x-component': 'Input.TextArea',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  integer: {
    type: 'integer',
    interface: 'integer',
    title: '整数',
    description: '整数数字',
    uiSchema: {
      type: 'number',
      'x-component': 'InputNumber',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  number: {
    type: 'float',
    interface: 'number',
    title: '数字',
    description: '小数数字',
    uiSchema: {
      type: 'number',
      'x-component': 'InputNumber',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  percent: {
    type: 'float',
    interface: 'percent',
    title: '百分比',
    description: '百分比数字',
    uiSchema: {
      type: 'number',
      'x-component': 'InputNumber',
      'x-component-props': {
        addonAfter: '%'
      },
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  boolean: {
    type: 'boolean',
    interface: 'checkbox',
    title: '布尔值',
    description: '是/否开关',
    uiSchema: {
      type: 'boolean',
      'x-component': 'Checkbox',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  date: {
    type: 'date',
    interface: 'date',
    title: '日期',
    description: '日期选择',
    uiSchema: {
      type: 'string',
      'x-component': 'DatePicker',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  time: {
    type: 'time',
    interface: 'time',
    title: '时间',
    description: '时间选择',
    uiSchema: {
      type: 'string',
      'x-component': 'TimePicker',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  datetime: {
    type: 'datetime',
    interface: 'datetime',
    title: '日期时间',
    description: '日期时间选择',
    uiSchema: {
      type: 'string',
      'x-component': 'DatePicker',
      'x-component-props': {
        showTime: true
      },
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  json: {
    type: 'json',
    interface: 'json',
    title: 'JSON',
    description: 'JSON 对象',
    uiSchema: {
      type: 'object',
      'x-component': 'Input.JSON',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  
  // 选择类型
  select: {
    type: 'string',
    interface: 'select',
    title: '下拉选择',
    description: '单选下拉框',
    uiSchema: {
      type: 'string',
      'x-component': 'Select',
      'x-decorator': 'FormItem'
    },
    options: {
      dataSource: [] // 需要配置选项
    },
    implemented: true
  },
  multipleSelect: {
    type: 'string',
    interface: 'multipleSelect',
    title: '多选下拉',
    description: '多选下拉框',
    uiSchema: {
      type: 'array',
      'x-component': 'Select',
      'x-component-props': {
        mode: 'multiple'
      },
      'x-decorator': 'FormItem'
    },
    options: {
      dataSource: []
    },
    implemented: true
  },
  radioGroup: {
    type: 'string',
    interface: 'radioGroup',
    title: '单选按钮组',
    description: '单选按钮组',
    uiSchema: {
      type: 'string',
      'x-component': 'Radio.Group',
      'x-decorator': 'FormItem'
    },
    options: {
      dataSource: []
    },
    implemented: true
  },
  checkboxGroup: {
    type: 'string',
    interface: 'checkboxGroup',
    title: '复选框组',
    description: '多选复选框',
    uiSchema: {
      type: 'array',
      'x-component': 'Checkbox.Group',
      'x-decorator': 'FormItem'
    },
    options: {
      dataSource: []
    },
    implemented: true
  },
  
  // 文件类型
  attachment: {
    type: 'string',
    interface: 'attachment',
    title: '附件',
    description: '文件上传',
    uiSchema: {
      type: 'string',
      'x-component': 'Upload.Attachment',
      'x-decorator': 'FormItem'
    },
    implemented: true
  },
  
  // 系统字段
  id: {
    type: 'bigInt',
    interface: 'id',
    title: 'ID',
    description: '主键ID',
    uiSchema: {
      type: 'number',
      title: '{{t("ID")}}',
      'x-component': 'InputNumber',
      'x-read-pretty': true
    },
    isSystemField: true,
    implemented: true
  },
  createdAt: {
    type: 'date',
    interface: 'createdAt',
    title: '创建时间',
    description: '记录创建时间',
    uiSchema: {
      type: 'datetime',
      title: '{{t("Created at")}}',
      'x-component': 'DatePicker',
      'x-read-pretty': true
    },
    isSystemField: true,
    implemented: true
  },
  updatedAt: {
    type: 'date',
    interface: 'updatedAt',
    title: '更新时间',
    description: '记录更新时间',
    uiSchema: {
      type: 'datetime',
      title: '{{t("Updated at")}}',
      'x-component': 'DatePicker',
      'x-read-pretty': true
    },
    isSystemField: true,
    implemented: true
  },
  createdBy: {
    type: 'belongsTo',
    interface: 'createdBy',
    title: '创建人',
    description: '记录创建者',
    uiSchema: {
      type: 'object',
      title: '{{t("Created by")}}',
      'x-component': 'DrawerSelect',
      'x-read-pretty': true
    },
    isSystemField: true,
    options: {
      target: 'users',
      foreignKey: 'createdById'
    },
    implemented: true
  },
  updatedBy: {
    type: 'belongsTo',
    interface: 'updatedBy',
    title: '更新人',
    description: '记录更新者',
    uiSchema: {
      type: 'object',
      title: '{{t("Updated by")}}',
      'x-component': 'DrawerSelect',
      'x-read-pretty': true
    },
    isSystemField: true,
    options: {
      target: 'users',
      foreignKey: 'updatedById'
    },
    implemented: true
  }
};

/**
 * Phase 1.1: 标识字段类型（待实现）
 */
export const IDENTIFIER_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  uid: {
    type: 'uid',
    interface: 'input',
    title: '短ID',
    description: '自动生成的短ID',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    options: {
      length: 11,
      prefix: '',
      charset: '0123456789abcdefghijklmnopqrstuvwxyz'
    },
    priority: 'high',
    implemented: true
  },
  uuid: {
    type: 'uuid',
    interface: 'input',
    title: 'UUID',
    description: '通用唯一标识符',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    options: {
      version: 4 // UUID v4
    },
    priority: 'high',
    implemented: true
  },
  nanoid: {
    type: 'nanoid',
    interface: 'input',
    title: 'NanoID',
    description: 'URL友好的短ID',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    options: {
      length: 21,
      alphabet: '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'
    },
    priority: 'high',
    implemented: true
  },
  sequence: {
    type: 'string',
    interface: 'input',
    title: '序列号',
    description: '按规则生成的序列号',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    options: {
      pattern: '{date}-{seq}', // 序列号模式
      digits: 4, // 序列号位数
      start: 1 // 起始值
    },
    requiredPlugins: ['@nocobase/plugin-field-sequence'],
    priority: 'high',
    implemented: false
  }
};

/**
 * Phase 1.2: 安全字段类型（待实现）
 */
export const SECURITY_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  password: {
    type: 'password',
    interface: 'password',
    title: '密码',
    description: '密码输入（使用 scrypt 加密存储）',
    uiSchema: {
      type: 'string',
      'x-component': 'Input.Password',
      'x-decorator': 'FormItem'
    },
    options: {
      length: 64, // 密码哈希总长度（盐值 + 派生密钥）
      randomBytesSize: 8, // 盐值随机字节大小
      minLength: 6, // 最小密码长度（UI验证）
      maxLength: 100 // 最大密码长度（UI验证）
    },
    implemented: true
  },
  encryption: {
    type: 'encryption',
    interface: 'input',
    title: '加密字段',
    description: '敏感数据 AES-256-CBC 加密存储',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem'
    },
    options: {
      // 环境变量配置：
      // ENCRYPTION_FIELD_KEY: 32字符密钥（必需）
      // ENCRYPTION_FIELD_IV: 16字符初始化向量（可选）
      algorithm: 'aes-256-cbc', // 加密算法
      iv: '' // 自定义初始化向量（可选，默认使用环境变量）
    },
    implemented: true
  }
};

/**
 * Phase 1.3: 时间相关字段类型（待实现）
 */
export const TIME_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  dateOnly: {
    type: 'dateOnly',
    interface: 'date',
    title: '仅日期',
    description: '仅包含日期，不包含时间',
    uiSchema: {
      type: 'string',
      'x-component': 'DatePicker',
      'x-decorator': 'FormItem'
    },
    priority: 'high',
    implemented: false
  },
  datetimeNoTz: {
    type: 'datetimeNoTz',
    interface: 'datetime',
    title: '日期时间（无时区）',
    description: '不包含时区信息的日期时间',
    uiSchema: {
      type: 'string',
      'x-component': 'DatePicker',
      'x-component-props': {
        showTime: true
      },
      'x-decorator': 'FormItem'
    },
    priority: 'high',
    implemented: false
  },
  datetimeTz: {
    type: 'datetimeTz',
    interface: 'datetime',
    title: '日期时间（带时区）',
    description: '包含时区信息的日期时间',
    uiSchema: {
      type: 'string',
      'x-component': 'DatePicker',
      'x-component-props': {
        showTime: true
      },
      'x-decorator': 'FormItem'
    },
    priority: 'high',
    implemented: false
  },
  unixTimestamp: {
    type: 'unixTimestamp',
    interface: 'datetime',
    title: 'Unix时间戳',
    description: 'Unix时间戳格式',
    uiSchema: {
      type: 'number',
      'x-component': 'DatePicker',
      'x-component-props': {
        showTime: true
      },
      'x-decorator': 'FormItem'
    },
    priority: 'high',
    implemented: false
  }
};

/**
 * Phase 2.1: 高级数据类型（待实现）
 */
export const ADVANCED_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  array: {
    type: 'array',
    interface: 'array',
    title: '数组',
    description: '数组类型字段',
    uiSchema: {
      type: 'array',
      'x-component': 'Array',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  },
  set: {
    type: 'set',
    interface: 'checkboxGroup',
    title: '集合',
    description: '集合类型字段',
    uiSchema: {
      type: 'array',
      'x-component': 'Checkbox.Group',
      'x-decorator': 'FormItem'
    },
    options: {
      options: [] // 预定义选项
    },
    priority: 'medium',
    implemented: false
  },
  radio: {
    type: 'string',
    interface: 'radio',
    title: '单选按钮',
    description: '单选按钮',
    uiSchema: {
      type: 'string',
      'x-component': 'Radio',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  },
  virtual: {
    type: 'virtual',
    interface: 'input',
    title: '虚拟字段',
    description: '计算字段，不存储在数据库',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    options: {
      // 需要配置计算逻辑
      get: () => '',
      set: () => {}
    },
    priority: 'medium',
    implemented: false
  }
};

/**
 * Phase 2.2: 格式化输入字段（待实现）
 */
export const FORMATTED_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  email: {
    type: 'string',
    interface: 'email',
    title: '邮箱',
    description: '邮箱地址',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-component-props': {
        prefix: '@'
      },
      'x-decorator': 'FormItem'
    },
    options: {
      pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
      message: '请输入有效的邮箱地址'
    },
    priority: 'medium',
    implemented: false
  },
  phone: {
    type: 'string',
    interface: 'phone',
    title: '电话',
    description: '电话号码',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem'
    },
    options: {
      pattern: '^1[3-9]\\d{9}$',
      message: '请输入有效的手机号码'
    },
    priority: 'medium',
    implemented: false
  },
  url: {
    type: 'string',
    interface: 'url',
    title: '链接',
    description: 'URL地址',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-component-props': {
        prefix: 'https://'
      },
      'x-decorator': 'FormItem'
    },
    options: {
      pattern: '^https?://.+',
      message: '请输入有效的URL地址'
    },
    priority: 'medium',
    implemented: false
  },
  color: {
    type: 'string',
    interface: 'color',
    title: '颜色',
    description: '颜色选择器',
    uiSchema: {
      type: 'string',
      'x-component': 'Input.Color',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  },
  icon: {
    type: 'string',
    interface: 'icon',
    title: '图标',
    description: '图标选择器',
    uiSchema: {
      type: 'string',
      'x-component': 'IconPicker',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  },
  markdown: {
    type: 'text',
    interface: 'markdown',
    title: 'Markdown',
    description: 'Markdown编辑器',
    uiSchema: {
      type: 'string',
      'x-component': 'Markdown',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  },
  richText: {
    type: 'text',
    interface: 'richText',
    title: '富文本',
    description: '富文本编辑器',
    uiSchema: {
      type: 'string',
      'x-component': 'RichText',
      'x-decorator': 'FormItem'
    },
    priority: 'medium',
    implemented: false
  }
};

/**
 * Phase 3: 地理位置字段（待实现）
 */
export const GEOGRAPHIC_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  point: {
    type: 'point',
    interface: 'point',
    title: '点坐标',
    description: '经纬度坐标点',
    uiSchema: {
      type: 'object',
      'x-component': 'Map.Drawer',
      'x-decorator': 'FormItem'
    },
    requiredPlugins: ['@nocobase/plugin-map'],
    priority: 'low',
    implemented: false
  },
  lineString: {
    type: 'lineString',
    interface: 'lineString',
    title: '线段',
    description: '线段轨迹',
    uiSchema: {
      type: 'object',
      'x-component': 'Map.Drawer',
      'x-decorator': 'FormItem'
    },
    requiredPlugins: ['@nocobase/plugin-map'],
    priority: 'low',
    implemented: false
  },
  polygon: {
    type: 'polygon',
    interface: 'polygon',
    title: '多边形',
    description: '多边形区域',
    uiSchema: {
      type: 'object',
      'x-component': 'Map.Drawer',
      'x-decorator': 'FormItem'
    },
    requiredPlugins: ['@nocobase/plugin-map'],
    priority: 'low',
    implemented: false
  },
  circle: {
    type: 'circle',
    interface: 'circle',
    title: '圆形',
    description: '圆形区域',
    uiSchema: {
      type: 'object',
      'x-component': 'Map.Drawer',
      'x-decorator': 'FormItem'
    },
    requiredPlugins: ['@nocobase/plugin-map'],
    priority: 'low',
    implemented: false
  }
};

/**
 * Phase 3: 特殊功能字段（待实现）
 */
export const SPECIAL_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  chinaRegion: {
    type: 'string',
    interface: 'chinaRegion',
    title: '中国地区',
    description: '中国地区选择器',
    uiSchema: {
      type: 'string',
      'x-component': 'ChinaRegion',
      'x-decorator': 'FormItem'
    },
    priority: 'low',
    implemented: false
  },
  context: {
    type: 'context',
    interface: 'input',
    title: '上下文字段',
    description: '上下文感知字段',
    uiSchema: {
      type: 'string',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    priority: 'low',
    implemented: false
  },
  snapshot: {
    type: 'json',
    interface: 'json',
    title: '快照',
    description: '数据快照',
    uiSchema: {
      type: 'object',
      'x-component': 'Input.JSON',
      'x-decorator': 'FormItem',
      'x-read-pretty': true
    },
    requiredPlugins: ['@nocobase/plugin-snapshot-field'],
    priority: 'low',
    implemented: false
  }
};

/**
 * 所有字段类型的完整映射
 */
export const ALL_FIELD_TYPES: Record<string, FieldTypeInfo> = {
  ...BASIC_FIELD_TYPES,
  ...IDENTIFIER_FIELD_TYPES,
  ...SECURITY_FIELD_TYPES,
  ...TIME_FIELD_TYPES,
  ...ADVANCED_FIELD_TYPES,
  ...FORMATTED_FIELD_TYPES,
  ...GEOGRAPHIC_FIELD_TYPES,
  ...SPECIAL_FIELD_TYPES
};

/**
 * 根据字段名推断字段类型
 */
export function inferFieldType(fieldName: string): FieldTypeInfo | null {
  const name = fieldName.toLowerCase();
  
  // ID 相关
  if (name.includes('id') && !name.includes('grid')) {
    return IDENTIFIER_FIELD_TYPES.uid || IDENTIFIER_FIELD_TYPES.uuid;
  }
  
  // 密码相关
  if (name.includes('password') || name.includes('pwd') || name.includes('passwd')) {
    return SECURITY_FIELD_TYPES.password;
  }
  
  // 邮箱相关
  if (name.includes('email') || name.includes('mail')) {
    return FORMATTED_FIELD_TYPES.email;
  }
  
  // 电话相关
  if (name.includes('phone') || name.includes('mobile') || name.includes('tel')) {
    return FORMATTED_FIELD_TYPES.phone;
  }
  
  // URL相关
  if (name.includes('url') || name.includes('link') || name.includes('website')) {
    return FORMATTED_FIELD_TYPES.url;
  }
  
  // 颜色相关
  if (name.includes('color') || name.includes('colour')) {
    return FORMATTED_FIELD_TYPES.color;
  }
  
  // 图标相关
  if (name.includes('icon')) {
    return FORMATTED_FIELD_TYPES.icon;
  }
  
  // 时间相关
  if (name.includes('time') || name.includes('date')) {
    if (name.includes('created') || name.includes('update')) {
      return BASIC_FIELD_TYPES.datetime;
    }
    if (name.includes('birth') || name.includes('birthday')) {
      return TIME_FIELD_TYPES.dateOnly;
    }
    return BASIC_FIELD_TYPES.date;
  }
  
  // 布尔相关
  if (name.includes('is') || name.includes('has') || name.includes('enabled') || name.includes('active')) {
    return BASIC_FIELD_TYPES.boolean;
  }
  
  // 数字相关
  if (name.includes('price') || name.includes('amount') || name.includes('money')) {
    return BASIC_FIELD_TYPES.number;
  }
  
  if (name.includes('count') || name.includes('num') || name.includes('quantity')) {
    return BASIC_FIELD_TYPES.integer;
  }
  
  if (name.includes('percent') || name.includes('rate')) {
    return BASIC_FIELD_TYPES.percent;
  }
  
  // 默认返回字符串
  return BASIC_FIELD_TYPES.string;
}

/**
 * 获取字段的默认配置
 */
export function getFieldDefaultConfig(type: string, options?: any): FieldTypeInfo | null {
  const fieldConfig = ALL_FIELD_TYPES[type];
  if (!fieldConfig) {
    return null;
  }
  
  return {
    ...fieldConfig,
    options: {
      ...fieldConfig.options,
      ...options
    }
  };
}

/**
 * 检查字段类型是否已实现
 */
export function isFieldTypeImplemented(type: string): boolean {
  return ALL_FIELD_TYPES[type]?.implemented || false;
}

/**
 * 获取待实现的高优先级字段类型
 */
export function getUnimplementedHighPriorityFields(): FieldTypeInfo[] {
  return Object.values(ALL_FIELD_TYPES).filter(
    field => !field.implemented && field.priority === 'high'
  );
}

/**
 * 获取需要特定插件的字段类型
 */
export function getPluginDependentFields(pluginName: string): FieldTypeInfo[] {
  return Object.values(ALL_FIELD_TYPES).filter(
    field => field.requiredPlugins?.includes(pluginName)
  );
}