import axios from 'axios';
import testConfig from './test-config.js';

// 对比手动创建的页面和API创建的页面的Schema差异
async function comparePageSchemas() {
  console.log('🔍 对比页面 Schema 结构差异\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    // 页面信息
    const manualPageUid = '2mk30f1pasa';  // 手动创建的"123"页面
    const apiPageUid = 'page-1754235700602-luqwmsxu9';  // API创建的MCP测试页面
    
    console.log('📋 对比页面信息:');
    console.log(`   - 手动创建页面: ${manualPageUid} ("123"页面)`);
    console.log(`   - API创建页面: ${apiPageUid} (MCP测试页面)`);
    
    // 1. 获取手动创建的页面Schema
    console.log('\n=== 获取手动创建的页面Schema ===');
    let manualSchema = null;
    try {
      const manualResponse = await client.get(`/uiSchemas:getJsonSchema/${manualPageUid}`);
      manualSchema = manualResponse.data.data;
      console.log('✅ 手动创建页面Schema获取成功');
    } catch (error) {
      console.log('❌ 获取手动创建页面Schema失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    // 2. 获取API创建的页面Schema
    console.log('\n=== 获取API创建的页面Schema ===');
    let apiSchema = null;
    try {
      const apiResponse = await client.get(`/uiSchemas:getJsonSchema/${apiPageUid}`);
      apiSchema = apiResponse.data.data;
      console.log('✅ API创建页面Schema获取成功');
    } catch (error) {
      console.log('❌ 获取API创建页面Schema失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    // 3. 显示完整Schema结构
    console.log('\n=== 完整Schema结构对比 ===');
    
    console.log('\n🔸 手动创建页面完整Schema:');
    console.log(JSON.stringify(manualSchema, null, 2));
    
    console.log('\n🔸 API创建页面完整Schema:');
    console.log(JSON.stringify(apiSchema, null, 2));

    // 4. 详细对比分析
    console.log('\n=== 详细对比分析 ===');
    
    // 对比根级属性
    console.log('\n📊 根级属性对比:');
    const manualRootKeys = Object.keys(manualSchema);
    const apiRootKeys = Object.keys(apiSchema);
    
    console.log(`   手动创建页面根属性: [${manualRootKeys.join(', ')}]`);
    console.log(`   API创建页面根属性: [${apiRootKeys.join(', ')}]`);
    
    const rootDiff = manualRootKeys.filter(key => !apiRootKeys.includes(key));
    const rootExtra = apiRootKeys.filter(key => !manualRootKeys.includes(key));
    
    if (rootDiff.length > 0) {
      console.log(`   🔍 手动页面独有属性: [${rootDiff.join(', ')}]`);
    }
    if (rootExtra.length > 0) {
      console.log(`   🔍 API页面独有属性: [${rootExtra.join(', ')}]`);
    }

    // 对比Schema结构
    console.log('\n📊 Schema结构对比:');
    const manualPageSchema = manualSchema.schema;
    const apiPageSchema = apiSchema.schema;
    
    if (manualPageSchema && apiPageSchema) {
      console.log('\n🔸 页面组件对比:');
      console.log(`   手动页面组件: ${manualPageSchema['x-component'] || 'none'}`);
      console.log(`   API页面组件: ${apiPageSchema['x-component'] || 'none'}`);
      
      console.log('\n🔸 页面属性数量对比:');
      const manualProps = Object.keys(manualPageSchema.properties || {});
      const apiProps = Object.keys(apiPageSchema.properties || {});
      console.log(`   手动页面属性: ${manualProps.length} [${manualProps.join(', ')}]`);
      console.log(`   API页面属性: ${apiProps.length} [${apiProps.join(', ')}]`);

      // 重点对比Grid组件
      console.log('\n🔍 Grid组件详细对比:');
      
      const manualGrid = manualPageSchema.properties?.grid;
      const apiGrid = apiPageSchema.properties?.grid;
      
      if (manualGrid && apiGrid) {
        console.log('\n✅ 两个页面都有Grid组件');
        
        console.log('\n📋 Grid组件属性对比:');
        console.log('🔸 手动创建页面的Grid:');
        console.log(`   - 组件: ${manualGrid['x-component']}`);
        console.log(`   - UID: ${manualGrid['x-uid'] || 'none'}`);
        console.log(`   - 初始化器: ${manualGrid['x-initializer'] || 'none'}`);
        console.log(`   - 类型: ${manualGrid.type}`);
        console.log(`   - 名称: ${manualGrid.name || 'none'}`);
        console.log(`   - 属性数量: ${Object.keys(manualGrid.properties || {}).length}`);
        
        console.log('\n🔸 API创建页面的Grid:');
        console.log(`   - 组件: ${apiGrid['x-component']}`);
        console.log(`   - UID: ${apiGrid['x-uid'] || 'none'}`);
        console.log(`   - 初始化器: ${apiGrid['x-initializer'] || 'none'}`);
        console.log(`   - 类型: ${apiGrid.type}`);
        console.log(`   - 名称: ${apiGrid.name || 'none'}`);
        console.log(`   - 属性数量: ${Object.keys(apiGrid.properties || {}).length}`);
        
        // 找出Grid的关键差异
        console.log('\n🎯 Grid关键差异:');
        const gridKeys = ['x-component', 'x-uid', 'x-initializer', 'type', 'name'];
        gridKeys.forEach(key => {
          const manualValue = manualGrid[key];
          const apiValue = apiGrid[key];
          if (manualValue !== apiValue) {
            console.log(`   ❗ ${key}: 手动="${manualValue}" vs API="${apiValue}"`);
          }
        });
        
        // 检查所有Grid属性
        const allManualGridKeys = Object.keys(manualGrid);
        const allApiGridKeys = Object.keys(apiGrid);
        
        const gridDiff = allManualGridKeys.filter(key => !allApiGridKeys.includes(key));
        const gridExtra = allApiGridKeys.filter(key => !allManualGridKeys.includes(key));
        
        if (gridDiff.length > 0) {
          console.log(`   🔍 手动Grid独有属性: [${gridDiff.join(', ')}]`);
          gridDiff.forEach(key => {
            console.log(`     - ${key}: ${JSON.stringify(manualGrid[key])}`);
          });
        }
        if (gridExtra.length > 0) {
          console.log(`   🔍 API Grid独有属性: [${gridExtra.join(', ')}]`);
          gridExtra.forEach(key => {
            console.log(`     - ${key}: ${JSON.stringify(apiGrid[key])}`);
          });
        }
        
      } else if (manualGrid) {
        console.log('❌ 只有手动页面有Grid组件');
      } else if (apiGrid) {
        console.log('❌ 只有API页面有Grid组件');
      } else {
        console.log('❌ 两个页面都没有Grid组件');
      }
    }

    // 5. 总结关键发现
    console.log('\n=== 关键发现总结 ===');
    
    console.log('\n🎯 可能导致"Add block"按钮不显示的原因:');
    
    if (manualPageSchema?.properties?.grid && apiPageSchema?.properties?.grid) {
      const manualGrid = manualPageSchema.properties.grid;
      const apiGrid = apiPageSchema.properties.grid;
      
      // 检查关键差异
      if (manualGrid['x-initializer'] !== apiGrid['x-initializer']) {
        console.log(`1. ❗ x-initializer 不同: "${manualGrid['x-initializer']}" vs "${apiGrid['x-initializer']}"`);
      }
      
      if (manualGrid['x-uid'] !== apiGrid['x-uid']) {
        console.log(`2. ❗ x-uid 不同: "${manualGrid['x-uid']}" vs "${apiGrid['x-uid']}"`);
      }
      
      if (!manualGrid['x-uid'] && apiGrid['x-uid']) {
        console.log('3. ❗ 手动页面的Grid没有x-uid，API页面有');
      }
      
      if (manualGrid['x-uid'] && !apiGrid['x-uid']) {
        console.log('4. ❗ API页面的Grid没有x-uid，手动页面有');
      }
    }
    
    console.log('\n🚀 建议的修复方案:');
    console.log('1. 使用手动创建页面的Grid配置作为模板');
    console.log('2. 确保x-initializer配置正确');
    console.log('3. 检查x-uid的生成和配置');
    console.log('4. 验证Grid组件的完整属性');

  } catch (error) {
    console.error('❌ 对比失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行对比
comparePageSchemas().catch(console.error);
