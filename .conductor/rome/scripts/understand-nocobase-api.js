import axios from 'axios';
import testConfig from './test-config.js';

// 系统性地理解 NocoBase API
async function understandNocoBaseAPI() {
  console.log('🔍 系统性理解 NocoBase API 工作原理\n');

  // 创建 axios 客户端
  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    console.log('=== 第一部分：理解页面结构 ===\n');

    // 1. 获取页面 Schema
    const pageUid = 'page-1754235700602-luqwmsxu9';
    console.log(`📋 1. 获取页面 Schema: ${pageUid}`);
    
    let pageSchema = null;
    try {
      const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      pageSchema = schemaResponse.data.data;
      console.log('✅ 页面 Schema 获取成功');
      console.log('📊 页面结构分析:');
      console.log(`   - 类型: ${pageSchema.type}`);
      console.log(`   - 组件: ${pageSchema['x-component']}`);
      console.log(`   - UID: ${pageSchema['x-uid']}`);
      console.log(`   - 属性数量: ${Object.keys(pageSchema.properties || {}).length}`);
      
      // 分析页面结构
      if (pageSchema.properties) {
        console.log('📋 页面属性:');
        Object.keys(pageSchema.properties).forEach(key => {
          const prop = pageSchema.properties[key];
          console.log(`   - ${key}: ${prop['x-component']} (UID: ${prop['x-uid'] || 'none'})`);
        });
      }
      
      // 保存完整的 schema 用于分析
      console.log('\n📄 完整页面 Schema:');
      console.log(JSON.stringify(pageSchema, null, 2));
      
    } catch (error) {
      console.log('❌ 获取页面 Schema 失败:', error.response?.status, error.response?.data || error.message);
      return;
    }

    console.log('\n=== 第二部分：理解 Grid 结构 ===\n');

    // 2. 分析 Grid 结构
    console.log('📋 2. 分析 Grid 结构');
    
    let gridInfo = null;
    if (pageSchema.properties) {
      // 查找 Grid 组件
      Object.keys(pageSchema.properties).forEach(key => {
        const prop = pageSchema.properties[key];
        if (prop['x-component'] === 'Grid') {
          gridInfo = {
            key,
            uid: prop['x-uid'],
            properties: prop.properties || {}
          };
          console.log(`✅ 找到 Grid 组件:`);
          console.log(`   - 属性名: ${key}`);
          console.log(`   - UID: ${prop['x-uid'] || 'none'}`);
          console.log(`   - 子属性数量: ${Object.keys(prop.properties || {}).length}`);
        }
      });
    }

    if (!gridInfo) {
      console.log('⚠️ 未找到 Grid 组件，页面可能是完全空白的');
      console.log('💡 这意味着我们需要先创建基础的 Grid 结构');
    }

    console.log('\n=== 第三部分：测试不同的插入方法 ===\n');

    // 3. 创建一个最简单的测试区块
    console.log('📋 3. 创建最简单的测试区块');
    
    const timestamp = Date.now();
    const testBlockUid = `test-block-${timestamp}`;
    
    const simpleBlock = {
      type: 'void',
      name: testBlockUid,
      'x-uid': testBlockUid,
      'x-component': 'CardItem',
      'x-component-props': {
        title: `🧪 API 测试区块 ${new Date().toLocaleTimeString()}`
      },
      properties: {
        content: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: `# 🎯 API 测试成功！\n\n这个区块是通过直接 API 调用创建的。\n\n**创建时间**: ${new Date().toLocaleString('zh-CN')}\n**区块 UID**: ${testBlockUid}\n**页面 UID**: ${pageUid}`
          }
        }
      }
    };

    console.log('📊 测试区块结构:');
    console.log(JSON.stringify(simpleBlock, null, 2));

    // 方法 1: 如果有 Grid，插入到 Grid 中
    if (gridInfo && gridInfo.uid) {
      console.log('\n🔸 方法 1: 插入到 Grid UID');
      try {
        const insertResponse = await client.post('/uiSchemas:insertAdjacent', {
          parentUid: gridInfo.uid,
          schema: simpleBlock,
          position: 'beforeEnd'
        });
        console.log('✅ 插入到 Grid UID 成功!');
        console.log('📥 响应:', JSON.stringify(insertResponse.data, null, 2));
      } catch (error) {
        console.log('❌ 插入到 Grid UID 失败:', error.response?.status, error.response?.data || error.message);
      }
    }

    // 方法 2: 插入到 Grid 路径
    if (gridInfo) {
      console.log('\n🔸 方法 2: 插入到 Grid 路径');
      try {
        const insertResponse = await client.post('/uiSchemas:insertAdjacent', {
          parentUid: `${pageUid}.${gridInfo.key}`,
          schema: simpleBlock,
          position: 'beforeEnd'
        });
        console.log('✅ 插入到 Grid 路径成功!');
        console.log('📥 响应:', JSON.stringify(insertResponse.data, null, 2));
      } catch (error) {
        console.log('❌ 插入到 Grid 路径失败:', error.response?.status, error.response?.data || error.message);
      }
    }

    // 方法 3: 直接插入到页面
    console.log('\n🔸 方法 3: 直接插入到页面');
    try {
      const insertResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: pageUid,
        schema: simpleBlock,
        position: 'beforeEnd'
      });
      console.log('✅ 直接插入到页面成功!');
      console.log('📥 响应:', JSON.stringify(insertResponse.data, null, 2));
    } catch (error) {
      console.log('❌ 直接插入到页面失败:', error.response?.status, error.response?.data || error.message);
    }

    // 方法 4: 使用 patch 方法
    console.log('\n🔸 方法 4: 使用 patch 方法');
    try {
      const patchResponse = await client.post('/uiSchemas:patch', {
        'x-uid': pageUid,
        schema: {
          properties: {
            [testBlockUid]: simpleBlock
          }
        }
      });
      console.log('✅ Patch 方法成功!');
      console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Patch 方法失败:', error.response?.status, error.response?.data || error.message);
    }

    console.log('\n=== 第四部分：验证结果 ===\n');

    // 4. 验证区块是否真的被添加
    console.log('📋 4. 验证区块是否被添加');
    
    try {
      const updatedSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const updatedSchema = updatedSchemaResponse.data.data;
      
      console.log('✅ 获取更新后的页面 Schema');
      
      // 比较前后差异
      const originalPropsCount = Object.keys(pageSchema.properties || {}).length;
      const updatedPropsCount = Object.keys(updatedSchema.properties || {}).length;
      
      console.log('📊 Schema 对比:');
      console.log(`   - 原始属性数量: ${originalPropsCount}`);
      console.log(`   - 更新后属性数量: ${updatedPropsCount}`);
      
      if (updatedPropsCount > originalPropsCount) {
        console.log('🎉 检测到新属性被添加!');
        
        // 找出新添加的属性
        const originalKeys = Object.keys(pageSchema.properties || {});
        const updatedKeys = Object.keys(updatedSchema.properties || {});
        const newKeys = updatedKeys.filter(key => !originalKeys.includes(key));
        
        if (newKeys.length > 0) {
          console.log('🆕 新添加的属性:');
          newKeys.forEach(key => {
            const prop = updatedSchema.properties[key];
            console.log(`   - ${key}: ${prop['x-component']} (UID: ${prop['x-uid']})`);
          });
        }
      } else {
        console.log('⚠️ 没有检测到新属性');
      }
      
      // 检查是否有我们的测试区块
      const hasTestBlock = JSON.stringify(updatedSchema).includes(testBlockUid);
      if (hasTestBlock) {
        console.log('🎯 找到我们的测试区块!');
      } else {
        console.log('❌ 没有找到我们的测试区块');
      }
      
    } catch (error) {
      console.log('❌ 验证失败:', error.response?.status, error.response?.data || error.message);
    }

    console.log('\n=== 总结 ===\n');
    console.log('🎯 API 理解测试完成!');
    console.log('\n📱 请刷新页面查看结果:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageUid}`);
    console.log('\n💡 如果看到新的区块，说明 API 调用成功!');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
understandNocoBaseAPI().catch(console.error);
