import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            console.log('📥 Raw output:', line);
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    console.log('📥 Response:', JSON.stringify(response, null, 2));
    
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`📤 Sending ${method} request:`, JSON.stringify(request, null, 2));
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function demonstrateBlockAddition() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🎯 NocoBase 区块添加演示 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize MCP connection');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'block-demo-client', version: '1.0.0' }
    });

    // 2. 获取可用的集合
    console.log('\n📋 Step 2: Get available collections');
    const collectionsResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });

    let collections = [];
    if (collectionsResult && collectionsResult.content && collectionsResult.content[0]) {
      const text = collectionsResult.content[0].text;
      const collectionMatches = text.match(/• (\w+) \(/g);
      if (collectionMatches) {
        collections = collectionMatches.map(m => m.replace(/• (\w+) \(/, '$1'));
      }
    }

    console.log(`✅ Found collections: ${collections.join(', ')}`);

    // 3. 获取页面 Schema（使用已知的 MCP 测试页面 UID）
    const pageUid = 'page-1754235700602-luqwmsxu9'; // MCP 测试页面的 UID
    console.log(`\n📋 Step 3: Get page schema for UID: ${pageUid}`);
    
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: pageUid }
    });

    // 4. 模拟添加区块（由于页面可能没有 Grid 容器，我们将演示区块模板的创建）
    console.log('\n📋 Step 4: Demonstrate block templates');

    // 4.1 显示可用的区块类型
    console.log('\n🔸 Available block types:');
    await mcpClient.sendRequest('tools/call', {
      name: 'list_block_types',
      arguments: {}
    });

    // 4.2 演示 Markdown 区块创建（假设有 Grid 容器）
    console.log('\n🔸 Creating Markdown block template:');
    const gridUid = 'demo-grid-uid'; // 模拟的 Grid UID
    
    try {
      await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: gridUid,
          title: '🎉 NocoBase MCP 演示',
          content: `# 欢迎使用 NocoBase MCP 区块系统！

## 🚀 功能特性

通过 **Model Context Protocol (MCP)** 工具，我们可以：

### 📊 数据区块
- **表格区块** - 展示和管理数据，支持分页、排序、筛选
- **表单区块** - 创建和编辑记录，支持字段验证
- **详情区块** - 查看单条记录的详细信息
- **看板区块** - 可视化项目管理和任务跟踪

### 📝 内容区块
- **Markdown 区块** - 支持富文本内容展示
- **图表区块** - 数据可视化展示
- **日历区块** - 时间管理和事件展示

## 🛠️ 技术优势

1. **程序化创建** - 通过 API 自动创建区块
2. **标准化配置** - 确保区块配置的一致性
3. **批量操作** - 支持批量创建和管理
4. **版本控制** - 支持 CI/CD 流程集成

## 📈 使用场景

- **快速原型** - 快速搭建页面原型
- **批量部署** - 批量创建相似页面
- **自动化运维** - 自动化页面维护
- **模板复用** - 标准化页面模板

---

*创建时间: ${new Date().toLocaleString('zh-CN')}*
*通过 NocoBase MCP 服务器自动生成*`
        }
      });
    } catch (error) {
      console.log('⚠️ Expected error (demo Grid UID):', error.message);
    }

    // 4.3 如果有集合，演示数据区块创建
    if (collections.length > 0) {
      console.log('\n🔸 Creating Table block template:');
      try {
        await mcpClient.sendRequest('tools/call', {
          name: 'add_table_block',
          arguments: {
            parentUid: gridUid,
            collectionName: collections[0],
            title: `${collections[0]} 数据管理中心`,
            position: 'beforeEnd'
          }
        });
      } catch (error) {
        console.log('⚠️ Expected error (demo Grid UID):', error.message);
      }

      console.log('\n🔸 Creating Form block template:');
      try {
        await mcpClient.sendRequest('tools/call', {
          name: 'add_form_block',
          arguments: {
            parentUid: gridUid,
            collectionName: collections[0],
            title: `新建 ${collections[0]} 记录`,
            type: 'create',
            position: 'beforeEnd'
          }
        });
      } catch (error) {
        console.log('⚠️ Expected error (demo Grid UID):', error.message);
      }
    }

    // 5. 总结演示结果
    console.log('\n📋 Step 5: Demo Summary');
    console.log('\n🎊 区块添加演示完成！');
    
    console.log('\n📱 实际使用方法：');
    console.log('1. 获取真实页面的 Schema UID');
    console.log('2. 从 Schema 中找到 Grid 容器的 UID');
    console.log('3. 使用 MCP 工具添加各种类型的区块');
    console.log('4. 区块会自动出现在页面上');

    console.log('\n🔗 测试页面地址：');
    console.log(`   ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/page-1754235700602-luqwmsxu9`);

    console.log('\n💡 区块类型说明：');
    console.log('   📊 Table Block - 数据表格，支持 CRUD 操作');
    console.log('   📝 Form Block - 数据表单，支持创建/编辑');
    console.log('   📄 Details Block - 详情展示，只读模式');
    console.log('   📖 Markdown Block - 文档内容，支持富文本');
    console.log('   📋 Kanban Block - 看板视图，项目管理');

    console.log('\n🚀 下一步：');
    console.log('   - 获取真实页面的 Grid UID');
    console.log('   - 实际添加区块到页面');
    console.log('   - 配置区块的详细属性');
    console.log('   - 测试区块的交互功能');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动演示
demonstrateBlockAddition().catch(console.error);
