import axios from 'axios';
import testConfig from './test-config.js';

// 修复区块位置 - 将区块添加到正确的 Grid 容器中
async function fixBlockPlacement() {
  console.log('🔧 修复区块位置 - 添加到正确的 Grid 容器中\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    
    // 1. 首先清理之前错误位置的区块
    console.log('📋 1. 清理之前错误位置的区块');
    
    try {
      // 获取当前 schema
      const currentSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const currentSchema = currentSchemaResponse.data.data;
      
      // 找到所有测试区块
      const testBlocks = Object.keys(currentSchema.schema.properties || {})
        .filter(key => key.includes('test-block-'));
      
      if (testBlocks.length > 0) {
        console.log(`🗑️ 找到 ${testBlocks.length} 个需要清理的测试区块:`, testBlocks);
        
        // 删除这些区块
        for (const blockUid of testBlocks) {
          try {
            await client.post('/uiSchemas:remove', {
              'x-uid': blockUid
            });
            console.log(`✅ 删除区块: ${blockUid}`);
          } catch (error) {
            console.log(`⚠️ 删除区块失败 ${blockUid}:`, error.response?.data || error.message);
          }
        }
      } else {
        console.log('ℹ️ 没有找到需要清理的测试区块');
      }
    } catch (error) {
      console.log('⚠️ 清理步骤失败:', error.message);
    }

    // 2. 获取页面的最新 Schema
    console.log('\n📋 2. 获取页面的最新 Schema');
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const pageSchema = schemaResponse.data.data;
    
    console.log('📊 页面结构:');
    console.log(JSON.stringify(pageSchema, null, 2));
    
    // 3. 确保 Grid 存在并获取其信息
    console.log('\n📋 3. 分析 Grid 结构');
    
    const gridProp = pageSchema.schema?.properties?.grid;
    if (!gridProp) {
      console.log('❌ 没有找到 Grid 组件，无法继续');
      return;
    }
    
    console.log('✅ 找到 Grid 组件:');
    console.log(`   - 组件: ${gridProp['x-component']}`);
    console.log(`   - UID: ${gridProp['x-uid'] || 'none'}`);
    console.log(`   - 初始化器: ${gridProp['x-initializer']}`);
    
    // 4. 创建新的测试区块
    console.log('\n📋 4. 创建新的测试区块');
    
    const timestamp = Date.now();
    const testBlockUid = `correct-block-${timestamp}`;
    
    const correctBlock = {
      type: 'void',
      name: testBlockUid,
      'x-uid': testBlockUid,
      'x-component': 'CardItem',
      'x-component-props': {
        title: `✅ 正确位置的测试区块 ${new Date().toLocaleTimeString()}`
      },
      properties: {
        content: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: `# 🎉 成功！区块在正确位置！

## 🎯 这次我们做对了！

这个区块被正确地添加到了 **Grid 容器** 中，而不是页面的根级属性中。

### 📊 技术细节

- **创建时间**: ${new Date().toLocaleString('zh-CN')}
- **区块 UID**: ${testBlockUid}
- **页面 UID**: ${pageUid}
- **正确路径**: page.schema.properties.grid.properties.${testBlockUid}

### 🔍 学到的经验

1. **NocoBase 区块必须在 Grid 容器中** - 不能直接添加到页面根级
2. **使用 patch 方法修改 Grid** - 而不是页面本身
3. **区块结构需要完整** - 包括 name, x-uid, x-component 等属性

---
*通过系统性的 API 理解和调试创建* 🚀`
          }
        }
      }
    };

    console.log('📊 新区块结构:');
    console.log(JSON.stringify(correctBlock, null, 2));

    // 5. 将区块添加到 Grid 中
    console.log('\n📋 5. 将区块添加到 Grid 中');
    
    // 方法：使用 patch 修改 Grid 的 properties
    try {
      // 如果 Grid 有 UID，直接 patch Grid
      if (gridProp['x-uid']) {
        console.log(`🔸 方法 1: 直接 patch Grid (UID: ${gridProp['x-uid']})`);
        const patchResponse = await client.post('/uiSchemas:patch', {
          'x-uid': gridProp['x-uid'],
          schema: {
            properties: {
              [testBlockUid]: correctBlock
            }
          }
        });
        console.log('✅ 直接 patch Grid 成功!');
        console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
      } else {
        // Grid 没有 UID，patch 页面的 grid 属性
        console.log('🔸 方法 2: patch 页面的 grid 属性');
        const patchResponse = await client.post('/uiSchemas:patch', {
          'x-uid': pageUid,
          schema: {
            properties: {
              grid: {
                ...gridProp,
                properties: {
                  ...(gridProp.properties || {}),
                  [testBlockUid]: correctBlock
                }
              }
            }
          }
        });
        console.log('✅ patch 页面的 grid 属性成功!');
        console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
      }
    } catch (error) {
      console.log('❌ 添加到 Grid 失败:', error.response?.status, error.response?.data || error.message);
    }

    // 6. 验证结果
    console.log('\n📋 6. 验证结果');
    
    try {
      const updatedSchemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const updatedSchema = updatedSchemaResponse.data.data;
      
      // 检查 Grid 中是否有我们的区块
      const gridProperties = updatedSchema.schema?.properties?.grid?.properties || {};
      const hasCorrectBlock = Object.keys(gridProperties).some(key => key.includes('correct-block-'));
      
      console.log('📊 验证结果:');
      console.log(`   - Grid 中的属性数量: ${Object.keys(gridProperties).length}`);
      console.log(`   - 包含正确区块: ${hasCorrectBlock}`);
      
      if (hasCorrectBlock) {
        console.log('🎉 成功！区块已正确添加到 Grid 中！');
        
        // 显示 Grid 中的所有属性
        console.log('📋 Grid 中的所有属性:');
        Object.keys(gridProperties).forEach(key => {
          const prop = gridProperties[key];
          console.log(`   - ${key}: ${prop['x-component']} (${prop['x-component-props']?.title || 'no title'})`);
        });
      } else {
        console.log('❌ 区块没有被正确添加到 Grid 中');
      }
      
    } catch (error) {
      console.log('❌ 验证失败:', error.response?.status, error.response?.data || error.message);
    }

    console.log('\n🎯 修复完成！');
    console.log('\n📱 请刷新页面查看结果:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageUid}`);
    console.log('\n💡 如果看到区块，说明我们找到了正确的方法！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行修复
fixBlockPlacement().catch(console.error);
