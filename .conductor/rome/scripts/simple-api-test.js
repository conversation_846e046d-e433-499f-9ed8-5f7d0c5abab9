#!/usr/bin/env node

/**
 * 简单的 API 连接测试
 */

import axios from 'axios';

async function testAPI() {
  console.log('🔗 Testing NocoBase API connection...\n');
  
  const config = {
    baseURL: 'https://n.astra.xin/api',
    headers: {
      'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM`,
      'X-App': 'mcp_playground',
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    timeout: 10000
  };
  
  const endpoints = [
    '/app:getInfo',
    '/collections:list',
    '/users:list'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing: ${endpoint}`);
      
      const response = await axios.get(endpoint, config);
      
      console.log(`✅ Success: ${response.status} ${response.statusText}`);
      console.log(`📊 Response size: ${JSON.stringify(response.data).length} bytes`);
      
      if (response.data && typeof response.data === 'object') {
        const keys = Object.keys(response.data);
        console.log(`🔑 Response keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
      }
      
      console.log(''); // 空行分隔
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ HTTP Error: ${error.response.status} ${error.response.statusText}`);
        if (error.response.data) {
          console.log(`📄 Error data: ${JSON.stringify(error.response.data).substring(0, 200)}...`);
        }
      } else if (error.request) {
        console.log(`❌ Network Error: ${error.code || 'No response received'}`);
      } else {
        console.log(`❌ Request Error: ${error.message}`);
      }
      console.log(''); // 空行分隔
    }
  }
}

// 运行测试
testAPI().then(() => {
  console.log('🏁 API test completed');
}).catch(error => {
  console.error('💥 Test runner crashed:', error);
  process.exit(1);
});
