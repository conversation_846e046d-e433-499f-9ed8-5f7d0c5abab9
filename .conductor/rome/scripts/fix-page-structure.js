import axios from 'axios';
import testConfig from './test-config.js';

// 修复页面结构 - 使用正确的简单结构
async function fixPageStructure() {
  console.log('🔧 修复页面结构 - 使用手动创建页面的简单结构\n');

  const client = axios.create({
    baseURL: testConfig.baseUrl,
    headers: {
      'Authorization': `Bearer ${testConfig.token}`,
      'X-App': testConfig.app,
      'Content-Type': 'application/json'
    }
  });

  try {
    const pageUid = 'page-1754235700602-luqwmsxu9';
    
    console.log('📋 修复策略:');
    console.log('   1. 使用手动创建页面的简单结构');
    console.log('   2. 创建空白的 Page 组件');
    console.log('   3. 让 NocoBase 自动添加 Grid 和初始化器');
    
    // 1. 获取当前页面状态
    console.log('\n📋 1. 获取当前页面状态');
    const currentResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
    const currentSchema = currentResponse.data.data;
    
    console.log('📊 当前页面结构:');
    console.log(JSON.stringify(currentSchema, null, 2));
    
    // 2. 创建正确的简单页面结构
    console.log('\n📋 2. 创建正确的简单页面结构');
    
    // 模仿手动创建的"123"页面的结构
    const correctPageStructure = {
      type: 'void',
      'x-component': 'Page',
      name: currentSchema.name, // 保持原有的 name
      'x-uid': pageUid,
      'x-async': false
    };
    
    console.log('📊 正确的页面结构:');
    console.log(JSON.stringify(correctPageStructure, null, 2));
    
    // 3. 使用 patch 方法更新页面结构
    console.log('\n📋 3. 更新页面结构');
    
    try {
      const patchResponse = await client.post('/uiSchemas:patch', {
        'x-uid': pageUid,
        ...correctPageStructure
      });
      
      console.log('✅ 页面结构更新成功!');
      console.log('📥 响应:', JSON.stringify(patchResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ 更新页面结构失败:', error.response?.status, error.response?.data || error.message);
      return;
    }
    
    // 4. 验证更新结果
    console.log('\n📋 4. 验证更新结果');
    
    try {
      const verifyResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      const verifiedSchema = verifyResponse.data.data;
      
      console.log('📊 更新后的页面结构:');
      console.log(JSON.stringify(verifiedSchema, null, 2));
      
      // 检查结构是否正确
      const isCorrectStructure = (
        verifiedSchema.type === 'void' &&
        verifiedSchema['x-component'] === 'Page' &&
        verifiedSchema['x-uid'] === pageUid &&
        !verifiedSchema.schema // 不应该有嵌套的 schema 属性
      );
      
      console.log('\n🔍 结构验证:');
      console.log(`   - 类型正确: ${verifiedSchema.type === 'void'}`);
      console.log(`   - 组件正确: ${verifiedSchema['x-component'] === 'Page'}`);
      console.log(`   - UID正确: ${verifiedSchema['x-uid'] === pageUid}`);
      console.log(`   - 无嵌套schema: ${!verifiedSchema.schema}`);
      console.log(`   - 整体结构正确: ${isCorrectStructure}`);
      
      if (isCorrectStructure) {
        console.log('🎉 页面结构修复成功！');
        console.log('💡 现在页面应该显示 "Add block" 按钮了');
      } else {
        console.log('⚠️ 页面结构还需要进一步调整');
      }
      
    } catch (error) {
      console.log('❌ 验证失败:', error.response?.status, error.response?.data || error.message);
    }
    
    console.log('\n🎯 修复完成！');
    console.log('\n📱 请在浏览器中验证:');
    console.log(`🔗 ${testConfig.baseUrl.replace('/api', '')}/apps/${testConfig.app}/admin/${pageUid}`);
    console.log('\n💡 步骤:');
    console.log('   1. 刷新页面');
    console.log('   2. 点击 UI Editor 按钮激活编辑模式');
    console.log('   3. 查看是否显示 "Add block" 按钮');
    
    console.log('\n🚀 如果成功，我们就找到了正确的页面创建方法！');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行修复
fixPageStructure().catch(console.error);
