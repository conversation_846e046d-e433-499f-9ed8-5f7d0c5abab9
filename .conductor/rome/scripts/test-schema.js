import { NocoBaseClient } from './dist/client.js';

const client = new NocoBaseClient({
  baseUrl: 'https://n.astra.xin/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
  app: 'mcp_playground'
});

async function testSchemaAnalysis() {
  try {
    console.log('🔍 Testing Schema Analysis...\n');
    
    // Test getting page schema
    const schemaUid = 'cafh7yoyd6w';
    console.log(`📄 Getting page schema for: ${schemaUid}`);
    
    const basicSchema = await client.getPageSchema(schemaUid);
    console.log('📋 Basic Schema:', JSON.stringify(basicSchema, null, 2));
    
    console.log('\n' + '='.repeat(80) + '\n');
    
    const properties = await client.getSchemaProperties(schemaUid);
    console.log('📁 Schema Properties:', JSON.stringify(properties, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testSchemaAnalysis();
