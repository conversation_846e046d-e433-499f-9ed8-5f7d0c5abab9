#!/usr/bin/env node

/**
 * 调试 NocoBase API 连接
 */

import axios from 'axios';

async function debugAPI() {
  console.log('🔍 Debugging NocoBase API connection...\n');
  
  const baseUrl = 'https://n.astra.xin/api';
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM';
  const app = 'mcp_playground';
  
  // 测试不同的请求头组合
  const testConfigs = [
    {
      name: 'Standard Bearer Token',
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-App': app,
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'Without X-App header',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'With X-Hostname header',
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-App': app,
        'X-Hostname': 'n.astra.xin',
        'Content-Type': 'application/json'
      }
    },
    {
      name: 'With additional headers',
      headers: {
        'Authorization': `Bearer ${token}`,
        'X-App': app,
        'X-Hostname': 'n.astra.xin',
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'NocoBase-MCP-Client/1.0'
      }
    }
  ];
  
  // 测试不同的端点
  const endpoints = [
    '/app:getInfo',
    '/collections:list',
    '/pm:listEnabled',
    '/systemSettings:get'
  ];
  
  for (const config of testConfigs) {
    console.log(`\n📋 Testing ${config.name}:`);
    console.log(`   Headers: ${JSON.stringify(config.headers, null, 2)}`);
    
    for (const endpoint of endpoints) {
      try {
        console.log(`\n   🔗 Testing endpoint: ${endpoint}`);
        
        const response = await axios({
          method: 'GET',
          url: `${baseUrl}${endpoint}`,
          headers: config.headers,
          timeout: 10000,
          validateStatus: (status) => status < 500 // 接受所有非服务器错误状态码
        });
        
        console.log(`   ✅ Status: ${response.status} ${response.statusText}`);
        console.log(`   📊 Response size: ${JSON.stringify(response.data).length} bytes`);
        
        if (response.data && typeof response.data === 'object') {
          const keys = Object.keys(response.data);
          console.log(`   🔑 Response keys: ${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}`);
        }
        
        // 如果成功，这就是工作的配置
        if (response.status === 200) {
          console.log(`\n🎉 SUCCESS! Found working configuration:`);
          console.log(`   URL: ${baseUrl}`);
          console.log(`   Headers: ${JSON.stringify(config.headers, null, 2)}`);
          console.log(`   Working endpoint: ${endpoint}`);
          return { baseUrl, headers: config.headers, endpoint };
        }
        
      } catch (error) {
        if (error.response) {
          console.log(`   ❌ HTTP Error: ${error.response.status} ${error.response.statusText}`);
          if (error.response.data) {
            console.log(`   📄 Error data: ${JSON.stringify(error.response.data).substring(0, 200)}...`);
          }
        } else if (error.request) {
          console.log(`   ❌ Network Error: No response received`);
          console.log(`   🔍 Request details: ${error.code || 'Unknown error'}`);
        } else {
          console.log(`   ❌ Request Error: ${error.message}`);
        }
      }
    }
  }
  
  console.log('\n💥 No working configuration found.');
  return null;
}

// 运行调试
debugAPI().then(result => {
  if (result) {
    console.log('\n✅ Use this configuration for your tests!');
  } else {
    console.log('\n❌ API connection debugging failed.');
    console.log('Please check:');
    console.log('1. Token is still valid');
    console.log('2. Network connectivity');
    console.log('3. NocoBase instance is accessible');
  }
}).catch(error => {
  console.error('💥 Debug script crashed:', error);
  process.exit(1);
});
