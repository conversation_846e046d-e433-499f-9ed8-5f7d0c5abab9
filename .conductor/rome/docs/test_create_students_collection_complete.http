POST https://n.astra.xin/api/collections:create
Content-Type: application/json
X-App: mcp_playground
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyMjk5NjYsImV4cCI6MzMzMTE4Mjk5NjZ9.LFyKzqaVQUCrWf7vxxhQlkvybjX1wmx9w-CZG12zsf0

{
  "name": "students",
  "title": "学生",
  "autoGenId": true,
  "createdAt": true,
  "updatedAt": true,
  "createdBy": true,
  "updatedBy": true,
  "fields": [
    {
      "name": "id",
      "type": "bigInt",
      "autoIncrement": true,
      "primaryKey": true,
      "allowNull": false,
      "interface": "id",
      "uiSchema": {
        "type": "number",
        "title": "{{t(\"ID\")}}",
        "x-component": "InputNumber",
        "x-read-pretty": true
      }
    },
    {
      "name": "name",
      "type": "string",
      "interface": "input",
      "uiSchema": {
        "type": "string",
        "title": "姓名",
        "x-component": "Input",
        "required": true
      }
    },
    {
      "name": "age",
      "type": "integer",
      "interface": "integer",
      "uiSchema": {
        "type": "number",
        "title": "年龄",
        "x-component": "InputNumber"
      }
    },
    {
      "name": "class",
      "type": "string",
      "interface": "input",
      "uiSchema": {
        "type": "string",
        "title": "班级",
        "x-component": "Input"
      }
    },
    {
      "name": "grade",
      "type": "string",
      "interface": "select",
      "uiSchema": {
        "type": "string",
        "title": "年级",
        "x-component": "Select",
        "enum": [
          {"label": "一年级", "value": "grade1"},
          {"label": "二年级", "value": "grade2"},
          {"label": "三年级", "value": "grade3"},
          {"label": "四年级", "value": "grade4"},
          {"label": "五年级", "value": "grade5"},
          {"label": "六年级", "value": "grade6"}
        ]
      }
    },
    {
      "name": "gender",
      "type": "string",
      "interface": "radioGroup",
      "uiSchema": {
        "type": "string",
        "title": "性别",
        "x-component": "Radio.Group",
        "enum": [
          {"label": "男", "value": "male"},
          {"label": "女", "value": "female"}
        ]
      }
    },
    {
      "name": "createdAt",
      "type": "date",
      "interface": "createdAt",
      "field": "createdAt",
      "uiSchema": {
        "type": "datetime",
        "title": "{{t(\"Created at\")}}",
        "x-component": "DatePicker",
        "x-component-props": {},
        "x-read-pretty": true
      }
    },
    {
      "name": "updatedAt",
      "type": "date",
      "interface": "updatedAt",
      "field": "updatedAt",
      "uiSchema": {
        "type": "datetime",
        "title": "{{t(\"Last updated at\")}}",
        "x-component": "DatePicker",
        "x-component-props": {},
        "x-read-pretty": true
      }
    },
    {
      "name": "createdBy",
      "type": "belongsTo",
      "interface": "createdBy",
      "target": "users",
      "foreignKey": "createdById",
      "uiSchema": {
        "type": "object",
        "title": "{{t(\"Created by\")}}",
        "x-component": "AssociationField",
        "x-component-props": {
          "fieldNames": {
            "value": "id",
            "label": "nickname"
          }
        },
        "x-read-pretty": true
      }
    },
    {
      "name": "updatedBy",
      "type": "belongsTo",
      "interface": "updatedBy",
      "target": "users",
      "foreignKey": "updatedById",
      "uiSchema": {
        "type": "object",
        "title": "{{t(\"Last updated by\")}}",
        "x-component": "AssociationField",
        "x-component-props": {
          "fieldNames": {
            "value": "id",
            "label": "nickname"
          }
        },
        "x-read-pretty": true
      }
    }
  ]
}
