# MCP Server 开发完整指南

## 概述

Model Context Protocol (MCP) 是一个标准化协议，允许应用程序以安全、标准化的方式为 LLM 应用程序提供数据和功能。本文档基于官方文档 (https://modelcontextprotocol.io/quickstart/server#node) 整理了如何开发 MCP Server 的完整指南。

## 什么是 MCP？

MCP 让你可以构建服务器，以安全、标准化的方式向 LLM 应用程序公开数据和功能。可以把它想象成专为 LLM 交互设计的 Web API。

### MCP 服务器的能力

- **Resources（资源）**: 通过类似 GET 端点的方式公开数据，用于将信息加载到 LLM 的上下文中
- **Tools（工具）**: 通过类似 POST 端点的方式提供功能，用于执行代码或产生副作用
- **Prompts（提示）**: 为 LLM 交互定义可重用的模板
- **Completions（补全）**: 为参数提供智能补全建议

## 核心概念

### 1. Resources - 上下文数据

Resources 提供对信息的结构化访问，应用程序可以检索这些信息并作为上下文提供给 AI 模型。

**特点**：
- 使用基于 URI 的标识
- 声明 MIME 类型以适当处理内容
- 支持直接资源（固定 URI）和资源模板（参数化 URI）
- 应用程序控制如何使用数据

**协议操作**：
- `resources/list` - 列出可用的直接资源
- `resources/templates/list` - 发现资源模板
- `resources/read` - 检索资源内容
- `resources/subscribe` - 监控资源变化

**用户交互模型**：Resources 是应用程序驱动的，主机可以灵活地检索、处理和呈现可用上下文。

### 2. Tools - AI 操作

Tools 使 AI 模型能够通过服务器实现的函数执行操作。每个工具定义一个具有类型化输入和输出的特定操作。

**特点**：
- 使用 JSON Schema 进行验证
- 每个工具执行单一操作
- 需要明确的用户批准
- 可以执行计算和产生副作用

**协议操作**：
- `tools/list` - 发现可用工具
- `tools/call` - 执行特定工具

**用户交互模型**：Tools 是模型控制的，AI 模型可以自动发现和调用它们。但 MCP 强调通过批准对话框进行人工监督。

### 3. Prompts - 交互模板

Prompts 提供可重用的模板，定义预期的输入和交互模式。

**特点**：
- 用户控制，需要明确调用
- 可以是上下文感知的
- 支持参数补全
- 结构化模板用于常见任务

**协议操作**：
- `prompts/list` - 发现可用提示
- `prompts/get` - 检索提示详情

**用户交互模型**：Prompts 是用户控制的，需要明确调用。应用程序通常通过斜杠命令、命令面板或专用 UI 按钮公开提示。

## 开发环境设置

### Node.js/TypeScript

#### 系统要求
- Node.js v18.x 或更高版本
- TypeScript 支持

#### 安装依赖
```bash
npm install @modelcontextprotocol/sdk
```

#### 基础服务器结构
```typescript
import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// 创建 MCP 服务器
const server = new McpServer({
  name: "demo-server",
  version: "1.0.0"
});

// 启动服务器
const transport = new StdioServerTransport();
await server.connect(transport);
```

### Python

#### 系统要求
- Python 3.10 或更高版本
- MCP SDK 1.2.0 或更高版本

#### 安装依赖
```bash
# 安装 uv
curl -LsSf https://astral.sh/uv/install.sh | sh

# 创建项目
uv init my-mcp-server
cd my-mcp-server

# 安装依赖
uv add "mcp[cli]" httpx
```

#### 基础服务器结构
```python
from mcp.server.fastmcp import FastMCP

# 初始化 FastMCP 服务器
mcp = FastMCP("my-server")

if __name__ == "__main__":
    # 初始化并运行服务器
    mcp.run(transport='stdio')
```

## 实现示例

### 1. 添加工具 (Tools)

#### TypeScript 示例
```typescript
// 简单计算工具
server.registerTool("add",
  {
    title: "Addition Tool",
    description: "Add two numbers",
    inputSchema: { a: z.number(), b: z.number() }
  },
  async ({ a, b }) => ({
    content: [{ type: "text", text: String(a + b) }]
  })
);

// 异步工具调用外部 API
server.registerTool(
  "fetch-weather",
  {
    title: "Weather Fetcher",
    description: "Get weather data for a city",
    inputSchema: { city: z.string() }
  },
  async ({ city }) => {
    const response = await fetch(`https://api.weather.com/${city}`);
    const data = await response.text();
    return {
      content: [{ type: "text", text: data }]
    };
  }
);

// 返回 ResourceLinks 的工具
server.registerTool(
  "list-files",
  {
    title: "List Files",
    description: "List project files",
    inputSchema: { pattern: z.string() }
  },
  async ({ pattern }) => ({
    content: [
      { type: "text", text: `Found files matching "${pattern}":` },
      // ResourceLinks 让工具返回引用而不是文件内容
      {
        type: "resource_link",
        uri: "file:///project/README.md",
        name: "README.md",
        mimeType: "text/markdown",
        description: 'A README file'
      }
    ]
  })
);
```

#### Python 示例
```python
@mcp.tool()
async def get_weather(city: str) -> str:
    """Get weather information for a city.

    Args:
        city: Name of the city
    """
    # 实现天气查询逻辑
    return f"Weather in {city}: Sunny, 25°C"

@mcp.tool()
async def calculate_bmi(weight_kg: float, height_m: float) -> str:
    """Calculate Body Mass Index.

    Args:
        weight_kg: Weight in kilograms
        height_m: Height in meters
    """
    bmi = weight_kg / (height_m * height_m)
    return f"BMI: {bmi:.2f}"
```

### 2. 添加资源 (Resources)

#### TypeScript 示例
```typescript
// 静态资源
server.registerResource(
  "config",
  "config://app",
  {
    title: "Application Config",
    description: "Application configuration data",
    mimeType: "text/plain"
  },
  async (uri) => ({
    contents: [{
      uri: uri.href,
      text: "App configuration here"
    }]
  })
);

// 动态资源模板
server.registerResource(
  "user-profile",
  new ResourceTemplate("users://{userId}/profile", { list: undefined }),
  {
    title: "User Profile",
    description: "User profile information"
  },
  async (uri, { userId }) => ({
    contents: [{
      uri: uri.href,
      text: `Profile data for user ${userId}`
    }]
  })
);

// 带上下文感知补全的资源
server.registerResource(
  "repository",
  new ResourceTemplate("github://repos/{owner}/{repo}", {
    list: undefined,
    complete: {
      // 基于先前解析的参数提供智能补全
      repo: (value, context) => {
        if (context?.arguments?.["owner"] === "org1") {
          return ["project1", "project2", "project3"].filter(r => r.startsWith(value));
        }
        return ["default-repo"].filter(r => r.startsWith(value));
      }
    }
  }),
  {
    title: "GitHub Repository",
    description: "Repository information"
  },
  async (uri, { owner, repo }) => ({
    contents: [{
      uri: uri.href,
      text: `Repository: ${owner}/${repo}`
    }]
  })
);
```

### 3. 添加提示 (Prompts)

#### TypeScript 示例
```typescript
import { completable } from "@modelcontextprotocol/sdk/server/completable.js";

server.registerPrompt(
  "review-code",
  {
    title: "Code Review",
    description: "Review code for best practices and potential issues",
    argsSchema: { code: z.string() }
  },
  ({ code }) => ({
    messages: [{
      role: "user",
      content: {
        type: "text",
        text: `Please review this code:\n\n${code}`
      }
    }]
  })
);

// 带上下文感知补全的提示
server.registerPrompt(
  "team-greeting",
  {
    title: "Team Greeting",
    description: "Generate a greeting for team members",
    argsSchema: {
      department: completable(z.string(), (value) => {
        // 部门建议
        return ["engineering", "sales", "marketing", "support"].filter(d => d.startsWith(value));
      }),
      name: completable(z.string(), (value, context) => {
        // 基于选定部门的姓名建议
        const department = context?.arguments?.["department"];
        if (department === "engineering") {
          return ["Alice", "Bob", "Charlie"].filter(n => n.startsWith(value));
        } else if (department === "sales") {
          return ["David", "Eve", "Frank"].filter(n => n.startsWith(value));
        }
        return ["Guest"].filter(n => n.startsWith(value));
      })
    }
  },
  ({ department, name }) => ({
    messages: [{
      role: "assistant",
      content: {
        type: "text",
        text: `Hello ${name}, welcome to the ${department} team!`
      }
    }]
  })
);
```

## 传输协议

### 1. STDIO 传输

适用于命令行工具和直接集成：

#### TypeScript
```typescript
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";

const transport = new StdioServerTransport();
await server.connect(transport);
```

#### Python
```python
if __name__ == "__main__":
    mcp.run(transport='stdio')
```

### 2. Streamable HTTP 传输

适用于远程服务器，处理客户端请求和服务器到客户端的通知。

#### 带会话管理的实现
```typescript
import express from "express";
import { randomUUID } from "node:crypto";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js"

const app = express();
app.use(express.json());

// 按会话 ID 存储传输的映射
const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

// 处理客户端到服务器通信的 POST 请求
app.post('/mcp', async (req, res) => {
  const sessionId = req.headers['mcp-session-id'] as string | undefined;
  let transport: StreamableHTTPServerTransport;

  if (sessionId && transports[sessionId]) {
    // 重用现有传输
    transport = transports[sessionId];
  } else if (!sessionId && isInitializeRequest(req.body)) {
    // 新的初始化请求
    transport = new StreamableHTTPServerTransport({
      sessionIdGenerator: () => randomUUID(),
      onsessioninitialized: (sessionId) => {
        transports[sessionId] = transport;
      },
      // DNS 重绑定保护默认禁用以保持向后兼容性
      // 如果在本地运行此服务器，请确保设置：
      // enableDnsRebindingProtection: true,
      // allowedHosts: ['127.0.0.1'],
    });

    // 传输关闭时清理
    transport.onclose = () => {
      if (transport.sessionId) {
        delete transports[transport.sessionId];
      }
    };

    const server = new McpServer({
      name: "example-server",
      version: "1.0.0"
    });

    // 连接到 MCP 服务器
    await server.connect(transport);
  } else {
    // 无效请求
    res.status(400).json({
      jsonrpc: '2.0',
      error: {
        code: -32000,
        message: 'Bad Request: No valid session ID provided',
      },
      id: null,
    });
    return;
  }

  // 处理请求
  await transport.handleRequest(req, res, req.body);
});

app.listen(3000);
```

#### CORS 配置（用于基于浏览器的客户端）

```typescript
import cors from 'cors';

// 在 MCP 路由之前添加 CORS 中间件
app.use(cors({
  origin: '*', // 生产环境中适当配置
  exposedHeaders: ['Mcp-Session-Id'],
  allowedHeaders: ['Content-Type', 'mcp-session-id'],
}));
```

## 测试和调试

### 使用 MCP Inspector

MCP Inspector 是官方提供的测试工具：

```bash
# 安装
npm install -g @modelcontextprotocol/inspector

# 测试服务器
mcp-inspector <server-command>
```

### 日志记录注意事项

**重要**：对于基于 STDIO 的服务器，永远不要写入标准输出 (stdout)：

```typescript
// ❌ 错误 (STDIO)
console.log("Processing request");

// ✅ 正确 (STDIO)
import logging from 'logging';
logging.info("Processing request");
```

```python
# ❌ 错误 (STDIO)
print("Processing request")

# ✅ 正确 (STDIO)
import logging
logging.info("Processing request")
```

写入 stdout 会破坏 JSON-RPC 消息并导致服务器故障。

## 与客户端集成

### Claude Desktop 配置

在 `~/Library/Application Support/Claude/claude_desktop_config.json` 中配置：

```json
{
  "mcpServers": {
    "my-server": {
      "command": "node",
      "args": ["path/to/server.js"]
    }
  }
}
```

### 自定义客户端

#### TypeScript 客户端示例

```typescript
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";

const transport = new StdioClientTransport({
  command: "node",
  args: ["server.js"]
});

const client = new Client({
  name: "example-client",
  version: "1.0.0"
});

await client.connect(transport);

// 列出工具
const tools = await client.listTools();

// 调用工具
const result = await client.callTool({
  name: "example-tool",
  arguments: { arg1: "value" }
});

// 列出资源
const resources = await client.listResources();

// 读取资源
const resource = await client.readResource({
  uri: "file:///example.txt"
});

// 请求补全
const completions = await client.complete({
  ref: {
    type: "ref/prompt",
    name: "example"
  },
  argument: {
    name: "argumentName",
    value: "partial"
  },
  context: {
    arguments: {
      previousArg: "value"
    }
  }
});
```

## 高级功能

### 1. 动态服务器

可以在服务器连接后动态添加/更新/删除工具、资源和提示：

```typescript
// 动态添加工具
const newTool = server.registerTool("dynamic-tool", ...);

// 禁用工具
newTool.disable();

// 启用工具
newTool.enable();

// 更新工具
newTool.update({ inputSchema: { newParam: z.string() } });

// 删除工具
newTool.remove();
```

### 2. 通知防抖以提高网络效率

当执行触发通知的批量更新时，可以启用通知防抖来提高性能：

```typescript
const server = new McpServer(
  { name: "efficient-server", version: "1.0.0" },
  {
    // 为特定方法启用通知防抖
    debouncedNotificationMethods: [
      'notifications/tools/list_changed',
      'notifications/resources/list_changed',
      'notifications/prompts/list_changed'
    ]
  }
);
```

### 3. 采样 (Sampling)

服务器可以请求连接的客户端进行 LLM 补全：

```typescript
// 在工具中使用 LLM 采样
server.registerTool(
  "summarize",
  {
    description: "Summarize any text using an LLM",
    inputSchema: { text: z.string().describe("Text to summarize") }
  },
  async ({ text }) => {
    const response = await server.server.createMessage({
      messages: [{
        role: "user",
        content: {
          type: "text",
          text: `Please summarize the following text concisely:\n\n${text}`
        }
      }],
      maxTokens: 500
    });

    return {
      content: [{
        type: "text",
        text: response.content.type === "text" ? response.content.text : "Unable to generate summary"
      }]
    };
  }
);
```

## 最佳实践

### 1. 错误处理

```typescript
server.registerTool(
  "safe-tool",
  { input: z.string() },
  async ({ input }) => {
    try {
      const result = await riskyOperation(input);
      return { content: [{ type: "text", text: result }] };
    } catch (error) {
      return {
        content: [{ type: "text", text: `Error: ${error.message}` }],
        isError: true
      };
    }
  }
);
```

### 2. 性能优化

使用 ResourceLinks 避免嵌入大文件内容：

```typescript
// 返回 ResourceLinks 而不是完整内容
server.registerTool(
  "list-files",
  { pattern: z.string() },
  async ({ pattern }) => ({
    content: [
      { type: "text", text: `Found files matching "${pattern}":` },
      {
        type: "resource_link",
        uri: "file:///project/README.md",
        name: "README.md",
        mimeType: "text/markdown",
        description: 'A README file'
      }
    ]
  })
);
```

### 3. 安全考虑

```typescript
const transport = new StreamableHTTPServerTransport({
  enableDnsRebindingProtection: true,
  allowedHosts: ['127.0.0.1'],
  allowedOrigins: ['https://yourdomain.com']
});
```

## 完整示例：天气服务器

```typescript
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// 创建 MCP 服务器
const server = new McpServer({
  name: "weather-server",
  version: "1.0.0"
});

// 常量
const NWS_API_BASE = "https://api.weather.gov";
const USER_AGENT = "weather-app/1.0";

// 辅助函数
async function makeNWSRequest(url: string): Promise<any | null> {
  const headers = {
    "User-Agent": USER_AGENT,
    "Accept": "application/geo+json"
  };

  try {
    const response = await fetch(url, { headers, timeout: 30000 });
    response.raise_for_status();
    return response.json();
  } catch (error) {
    return null;
  }
}

// 添加天气警报工具
server.registerTool(
  "get_alerts",
  {
    title: "Get Weather Alerts",
    description: "Get weather alerts for a US state",
    inputSchema: {
      state: z.string().describe("Two-letter US state code (e.g. CA, NY)")
    }
  },
  async ({ state }) => {
    const url = `${NWS_API_BASE}/alerts/active/area/${state}`;
    const data = await makeNWSRequest(url);

    if (!data || !data.features) {
      return {
        content: [{ type: "text", text: "Unable to fetch alerts or no alerts found." }]
      };
    }

    if (data.features.length === 0) {
      return {
        content: [{ type: "text", text: "No active alerts for this state." }]
      };
    }

    const alerts = data.features.map((feature: any) => {
      const props = feature.properties;
      return `
Event: ${props.event || 'Unknown'}
Area: ${props.areaDesc || 'Unknown'}
Severity: ${props.severity || 'Unknown'}
Description: ${props.description || 'No description available'}
Instructions: ${props.instruction || 'No specific instructions provided'}
`;
    });

    return {
      content: [{ type: "text", text: alerts.join("\n---\n") }]
    };
  }
);

// 启动服务器
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("Weather MCP server is running...");
}

main().catch((error) => {
  console.error("Server error:", error);
  process.exit(1);
});
```

## 故障排除

### 常见问题

1. **服务器未在 Claude Desktop 中显示**
   - 检查配置文件语法
   - 确保路径是绝对路径
   - 重启 Claude Desktop

2. **工具调用失败**
   - 检查 Claude 日志：`~/Library/Logs/Claude/mcp*.log`
   - 验证服务器构建和运行无错误
   - 尝试重启 Claude Desktop

3. **STDIO 通信问题**
   - 确保没有写入 stdout
   - 检查 JSON-RPC 消息格式
   - 验证传输协议实现

### 调试技巧

```bash
# 查看 Claude Desktop 日志
tail -n 20 -f ~/Library/Logs/Claude/mcp*.log

# 使用 MCP Inspector 测试
mcp-inspector node server.js

# 启用详细日志
DEBUG=mcp:* node server.js
```

## 资源和参考

- [官方文档](https://modelcontextprotocol.io)
- [MCP 规范](https://spec.modelcontextprotocol.io)
- [TypeScript SDK](https://github.com/modelcontextprotocol/typescript-sdk)
- [Python SDK](https://github.com/modelcontextprotocol/python-sdk)
- [示例服务器](https://github.com/modelcontextprotocol/servers)
- [MCP Inspector](https://github.com/modelcontextprotocol/inspector)

## 总结

MCP 为构建 LLM 应用程序提供了强大而灵活的框架。通过遵循本指南，你可以：

1. **理解核心概念**：Resources（上下文数据）、Tools（AI 操作）、Prompts（交互模板）
2. **选择合适的技术栈**：Node.js/TypeScript 或 Python
3. **实现功能丰富的服务器**：工具、资源、提示和高级功能
4. **选择适当的传输协议**：STDIO 用于本地集成，HTTP 用于远程服务
5. **与客户端集成**：Claude Desktop 或自定义客户端
6. **应用最佳实践**：错误处理、性能优化、安全考虑

### 关键要点

- **用户控制**：Tools 需要用户批准，确保安全性
- **应用程序驱动**：Resources 由应用程序控制如何使用
- **标准化协议**：使用 JSON-RPC 进行通信
- **类型安全**：使用 JSON Schema 进行验证
- **可扩展性**：支持动态添加/删除功能

记住始终遵循最佳实践，特别是在错误处理、安全性和性能方面，以构建高质量的 MCP 服务器。MCP 的真正力量在于多个服务器协同工作，通过统一接口结合它们的专业能力。