# MCP Server for NocoBase

A Model Context Protocol (MCP) server that provides tools and resources for interacting with NocoBase applications.

## Features

- **Collections Management**: Create, read, update, and delete NocoBase collections
- **Records Operations**: CRUD operations on collection records
- **Schema Inspection**: Explore collection schemas and field definitions
- **API Integration**: Direct integration with NocoBase REST API
- **Authentication**: Support for NocoBase authentication tokens

## Installation

```bash
npm install -g mcp-server-nocobase
```

## Usage

### With Claude Desktop

Add to your Claude Desktop configuration (`~/Library/Application Support/Claude/claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "nocobase": {
      "command": "mcp-server-nocobase",
      "args": [
        "--base-url", "https://your-nocobase-instance.com",
        "--token", "your-auth-token",
        "--app", "your-app-name"
      ]
    }
  }
}
```

### Command Line Options

- `--base-url`: NocoBase instance URL (required)
- `--token`: Authentication token (required)
- `--app`: Application name (required)
- `--help`: Show help information

## Available Tools

### Collections Management
- `list_collections`: List all collections
- `get_collection`: Get collection details
- `create_collection`: Create a new collection
- `update_collection`: Update collection configuration
- `delete_collection`: Delete a collection

### Records Operations
- `list_records`: List records from a collection
- `get_record`: Get a specific record
- `create_record`: Create a new record
- `update_record`: Update an existing record
- `delete_record`: Delete a record

### Schema Operations
- `get_collection_schema`: Get collection schema
- `list_fields`: List collection fields
- `create_field`: Add a field to collection

## Available Resources

- `collections://list`: List of all collections
- `collections://{name}`: Specific collection details
- `collections://{name}/schema`: Collection schema
- `collections://{name}/records`: Collection records

## Development

```bash
# Clone the repository
git clone https://github.com/your-username/mcp-server-nocobase.git
cd mcp-server-nocobase

# Install dependencies
npm install

# Build the project
npm run build

# Run in development mode
npm run dev

# Run tests
npm test
```

## Configuration

The server can be configured through environment variables:

- `NOCOBASE_BASE_URL`: NocoBase instance URL
- `NOCOBASE_TOKEN`: Authentication token
- `NOCOBASE_APP`: Application name

## License

MIT

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
